#include "DisturbanceEstimator.h"
#include "Math/orientation_tools.h"

DisturbanceEstimator::DisturbanceEstimator(float _dt, int _iterations_between_mpc, MIT_UserParameters* parameters) {
    dt =_dt;
    iterations_between_mpc = _iterations_between_mpc;
    dtMPC = dt * iterations_between_mpc;
    _parameters = parameters;
}

void DisturbanceEstimator::initialize() {
    Eigen::Matrix<float,3,1> Id;
    Id << 1.58460467e-01, 4.68645637e-01, 5.24474661e-01;
    I_body.diagonal() = Id;
    K = 50.0f * Eigen::Matrix<float,3,3>::Identity();
    mp = 0.0f; //基于力传感器估计的质量
    mp_torque_based = 0.0f;  // 新增：基于扭矩估计的质量
    disturbance.setZero();
    for(int i = 0; i < 4; i++) {
    foot_force_tau_est[i].setZero();
    }
    gravity << 0.0f, 0.0f, -9.81f; // 重力矢量
    // 初始化PID控制器
    initializePIDControllers();
}

void DisturbanceEstimator::run(const ControlFSMData<float>& data) {
    body_mass = 14;
    auto& estResult = data._stateEstimator->getResult();
    foot_force_est = estResult.footForceEstimate;
    contact_state = estResult.contactEstimate;
    // 获取完整的三维加速度信息
    acc_world_3d = estResult.aWorld;
    // 获取机器人姿态
    Mat3<float> R = estResult.rBody;

    // 基于关节扭矩传感器观测值估计足端接触力
    estimateFootForceFromTorque(data);

    // 统计接触状态中零的个数（假设contact_state[i]为0表示不接触）
    int contact_count = 0;
    for (int i = 0; i < 4; i++) {
        if (contact_state[i] > 0.1f) {
            contact_count++;
        }
    }

    // 只有当恰好两条腿接触时才更新
    if (contact_count == 4) {
        // 方法2: 基于扭矩估计的三维力估计负载质量
        // 获取机体到世界坐标系的旋转矩阵
        Mat3<float> R = estResult.rBody.transpose(); // 世界到机体的转置 = 机体到世界

        // 简化版本：先只使用Z分量（垂直力）
        float total_force_z_tau = 0.0f;
        total_contact_force_tau_est = Vec3<float>::Zero();

        for (int i = 0; i < 4; i++) {
            if (contact_state[i] > 0.1f) {
                // 只使用Z分量（垂直方向的接触力）
                float force_z = fabs(foot_force_tau_est[i][2]); // 取绝对值确保为正
                total_force_z_tau += force_z;

                // 暂时假设垂直方向就是世界坐标系的Z方向
                total_contact_force_tau_est[2] += force_z;
            }
        }

        if ((acc_world_3d - gravity).norm() > 1e-3f) {
            // 如果接触力太小，使用理论最小值
            if (total_contact_force < 50.0f) {
                total_contact_force = body_mass * 9.81f; // 使用机器人重量作为最小接触力
                std::cout << "Warning: Contact force too small, using theoretical minimum: "
                          << total_contact_force << " N" << std::endl;
            }
            // 方法1: 基于原有foot_force_est估计负载质量（只使用垂直分量）
            float total_contact_force = 0.0f;
            for (int i = 0; i < 4; i++) {
                if (contact_state[i] > 0.1f) {
                    total_contact_force += foot_force_est[i];
                }
            }
            
            // 计算两种方法的质量估计
            // 方法1: 基于原有foot_force_est（只使用垂直分量）
            float denominator = -9.81f + acc_world_3d[2];
            if (fabs(denominator) < 0.1f) { // 避免除以接近零的数
                denominator = 9.81f; // 使用重力加速度作为默认值
                total_mass_original = total_contact_force / denominator;
            } else {
                total_mass_original = total_contact_force / acc_world_3d[2];
            }
            float mp_new_original = total_mass_original - body_mass;

            // 合理性检查：负载质量应该在合理范围内
            if (mp_new_original < -5.0f) mp_new_original = -5.0f;  // 最小负载（可能是传感器误差）
            if (mp_new_original > 20.0f) mp_new_original = 20.0f;    // 最大负载限制

            // 方法2: 基于扭矩估计的垂直力（简化版本）
            // 只使用垂直方向的力和加速度
            float total_mass_torque = total_force_z_tau / (acc_world_3d[2]);
            float mp_new_torque = total_mass_torque - body_mass;

            // 合理性检查：扭矩估计的负载质量
            if (mp_new_torque < -10.0f) mp_new_torque = -10.0f;
            if (mp_new_torque > 20.0f) mp_new_torque = 20.0f;

            // 低通滤波
            float alpha = 0.1f;
            mp = alpha * mp_new_original + (1 - alpha) * mp;
            mp_torque_based = alpha * mp_new_torque + (1 - alpha) * mp_torque_based;
            
            // 在质量层面进行融合
            // 根据接触状态和质量估计的合理性分配权重
            
            // 计算扭矩估计的置信度
            torque_confidence = 1.0f;
            
            // 检查力的方向是否合理（应该大致向上）
            Vec3<float> avg_force_direction = total_contact_force_tau_est.normalized();
            float vertical_alignment = fabs(avg_force_direction[2]); // Z分量应该接近1
            
            // 检查力的幅值是否合理
            float force_magnitude = total_contact_force_tau_est.norm();
            float expected_force = body_mass * 9.81f; // 预期的大致力值
            
            // 根据对齐程度和力的大小调整置信度
            if (vertical_alignment < 0.8f) {
                torque_confidence *= 0.5f; // 力方向不够垂直，降低置信度
            }
            
            if (fabs(force_magnitude - expected_force) > 0.5f * expected_force) {
                torque_confidence *= 0.5f; // 力大小不合理，降低置信度
            }
            
            // 根据置信度分配权重
            float weight_torque = 0.5f * torque_confidence;
            float weight_original = 1.0f - weight_torque;
            
            // 如果扭矩估计的质量明显不合理，进一步降低其权重
            if (mp_new_torque < -5.0f || mp_new_torque > 30.0f) {
                weight_torque *= 0.4f;
                weight_original = 1.0f - weight_torque;
            }
            
            // 融合两种质量估计
            mp_fused = weight_original * mp + weight_torque * mp_torque_based;
            
            // 限制融合后的质量在合理范围内
            if (mp_fused < -5.0f) mp_fused = -5.0f;
            if (mp_fused > 20.0f) mp_fused = 20.0f;
        }
    }

    // 调试输出（每500次输出一次）
    static int counter = 0;
    if (counter++ >= 1000) {
        counter = 0;

        // 添加详细的调试信息
        std::cout << "=== Detailed Torque Analysis ===" << std::endl;
        // for (int i = 0; i < 4; i++) {
        //     if (contact_state[i] > 0.1f) {
        //         std::cout << "Leg " << i << " (in contact):" << std::endl;
        //         std::cout << "  tau_est: ["
        //                   << data._legController->datas[i].tauEstimate[0] << ", "
        //                   << data._legController->datas[i].tauEstimate[1] << ", "
        //                   << data._legController->datas[i].tauEstimate[2] << "]" << std::endl;

        //         // 计算重力补偿
        //         Vec3<float> q = data._legController->datas[i].q;
        //         Vec3<float> tau_gravity = computeGravityCompensation(q, i, data);
        //         std::cout << "  tau_gravity: ["
        //                   << tau_gravity[0] << ", " << tau_gravity[1] << ", " << tau_gravity[2] << "]" << std::endl;

        //         Vec3<float> tau_contact = data._legController->datas[i].tauEstimate - tau_gravity;
        //         std::cout << "  tau_contact: ["
        //                   << tau_contact[0] << ", " << tau_contact[1] << ", " << tau_contact[2] << "]" << std::endl;

        //         std::cout << "  foot_force_tau_est: ["
        //                   << foot_force_tau_est[i][0] << ", " << foot_force_tau_est[i][1] << ", "
        //                   << foot_force_tau_est[i][2] << "]" << std::endl;
        //         std::cout << "foot_force_est: ["
        //                   << foot_force_est[i] << "]" << std::endl;
        //     }
        // }
        // std::cout << "=== Disturbance Estimator (3D) ===" << std::endl;
        // std::cout << "Contact count: " << contact_count << std::endl;
        // std::cout << "Total force (torque-based, 3D): " << total_contact_force_tau_est.norm() << " N" << std::endl;
        // std::cout << "Total force vector (torque-based): [" << total_contact_force_tau_est[0]
        //           << ", " << total_contact_force_tau_est[1] << ", " << total_contact_force_tau_est[2] << "]" << std::endl;
        // std::cout << "Total force (original): " << total_contact_force << " N" << std::endl;
        // std::cout << "Acceleration (3D): " << acc_world_3d.transpose() << " m/s^2" << std::endl;
        std::cout << "MP (original): " << mp << " kg" << std::endl;
        std::cout << "MP (torque-based, 3D): " << mp_torque_based << " kg" << std::endl;
        std::cout << "MP (fused): " << mp_fused << " kg" << std::endl;
        std::cout << "Torque confidence: " << torque_confidence << std::endl;
    }
}

void DisturbanceEstimator::estimateFootForceFromTorque(const ControlFSMData<float>& data) {
    // 检查是否启用扭矩估计
    if (!enable_torque_estimation) {
        for (int i = 0; i < 4; i++) {
            foot_force_tau_est[i].setZero();
            foot_force_tau_est[i][2] = foot_force_est[i]; // 如果未启用，直接复制原有估计
        }
        return;
    }

    // 获取关节扭矩传感器数据和雅可比矩阵
    for (int leg = 0; leg < 4; leg++) {
        // 获取当前腿的关节扭矩观测值
        Vec3<float> tau_est = data._legController->datas[leg].tauEstimate;

        // 获取当前腿的关节位置和速度
        Vec3<float> q = data._legController->datas[leg].q;
        Vec3<float> qd = data._legController->datas[leg].qd;

        // 获取当前腿的雅可比矩阵（已在LegController中计算）
        Mat3<float> J = data._legController->datas[leg].J;

        // 计算重力补偿扭矩（简化模型）
        Vec3<float> tau_gravity = Vec3<float>::Zero();
        if (enable_gravity_compensation) {
            tau_gravity = computeGravityCompensation(q, leg, data);
        }

        // 计算惯性补偿扭矩（简化模型）
        Vec3<float> tau_inertia = Vec3<float>::Zero();
        if (enable_inertia_compensation) {
            tau_inertia = computeInertiaCompensation(q, qd, leg, data);
        }

        // 从观测扭矩中减去重力和惯性项，得到接触力产生的扭矩
        Vec3<float> tau_contact = tau_est - tau_gravity - tau_inertia;

        // 通过逆向动力学计算足端接触力
        // 公式: tau_contact = J^T * F_foot
        // 求解: F_foot = (J^T)^(-1) * tau_contact

        // 计算雅可比矩阵转置的伪逆
        Mat3<float> J_transpose = J.transpose();
        Mat3<float> J_transpose_pinv;

        // 使用SVD计算伪逆矩阵
        Eigen::JacobiSVD<Mat3<float>> svd(J_transpose, Eigen::ComputeFullU | Eigen::ComputeFullV);
        float tolerance = 1e-6f;

        // 计算伪逆
        auto singular_values = svd.singularValues();
        Mat3<float> sigma_inv = Mat3<float>::Zero();
        for (int i = 0; i < 3; i++) {
            if (singular_values(i) > tolerance) {
                sigma_inv(i, i) = 1.0f / singular_values(i);
            }
        }
        J_transpose_pinv = svd.matrixV() * sigma_inv * svd.matrixU().transpose();

        // 计算足端接触力
        // 注意：这里的符号很重要，需要根据实际的坐标系约定调整
        Vec3<float> foot_force = J_transpose_pinv * tau_contact;
        
        // 简单的低通滤波
        static Vec3<float> foot_force_tau_est_prev[4] = {Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero()};
        foot_force_tau_est[leg] = torque_filter_alpha * foot_force_tau_est_prev[leg] +
                                  (1.0f - torque_filter_alpha) * foot_force;
        foot_force_tau_est_prev[leg] = foot_force_tau_est[leg];
    }
}

// computeGravityCompensation 和 computeInertiaCompensation 函数保持不变
Vec3<float> DisturbanceEstimator::computeGravityCompensation(const Vec3<float>& q, int leg, const ControlFSMData<float>& data) {
    // 简化的重力补偿模型
    // 基于腿部连杆质量和重力的影响

    // 腿部连杆参数（简化模型）
    float m1 = 0.54f;  // abad连杆质量 (kg)
    float m2 = 0.634f; // hip连杆质量 (kg)
    float m3 = 0.064f; // knee连杆质量 (kg)

    float l1 = data._quadruped->_abadLinkLength;
    float l2 = data._quadruped->_hipLinkLength;
    float l3 = data._quadruped->_kneeLinkLength;

    // 重心位置（简化为连杆中点）
    float lc1 = l1 * 0.5f;
    float lc2 = l2 * 0.5f;
    float lc3 = l3 * 0.5f;

    float g = 9.81f;

    // 关节角度
    float q1 = q[0]; // abad
    float q2 = q[1]; // hip
    float q3 = q[2]; // knee

    // 计算重力扭矩（简化模型）
    Vec3<float> tau_gravity;

    // abad关节重力扭矩
    tau_gravity[0] = 0.0f; // abad轴通常垂直，重力扭矩较小

    // hip关节重力扭矩
    tau_gravity[1] = g * (m2 * lc2 * cos(q2) + m3 * (l2 * cos(q2) + lc3 * cos(q2 + q3)));

    // knee关节重力扭矩
    tau_gravity[2] = g * m3 * lc3 * cos(q2 + q3);

    return tau_gravity;
}

Vec3<float> DisturbanceEstimator::computeInertiaCompensation(const Vec3<float>& q, const Vec3<float>& qd, int leg, const ControlFSMData<float>& data) {
    // 简化的惯性补偿模型
    // 基于腿部连杆惯性和科里奥利力的影响

    // 腿部连杆参数（简化模型）
    float m1 = 0.54f;  // abad连杆质量 (kg)
    float m2 = 0.634f; // hip连杆质量 (kg)
    float m3 = 0.064f; // knee连杆质量 (kg)

    float l1 = data._quadruped->_abadLinkLength;
    float l2 = data._quadruped->_hipLinkLength;
    float l3 = data._quadruped->_kneeLinkLength;

    // 惯性参数（简化）
    float I1 = m1 * l1 * l1 / 12.0f;
    float I2 = m2 * l2 * l2 / 12.0f;
    float I3 = m3 * l3 * l3 / 12.0f;

    // 关节角度和角速度
    float q1 = q[0], q2 = q[1], q3 = q[2];
    float qd1 = qd[0], qd2 = qd[1], qd3 = qd[2];

    // 简化的惯性扭矩计算（主要考虑科里奥利力和离心力）
    Vec3<float> tau_inertia;

    // 简化模型：主要考虑关节间的耦合效应
    tau_inertia[0] = I1 * 0.1f * (qd2 * qd2 + qd3 * qd3); // abad轴受其他轴影响
    tau_inertia[1] = I2 * 0.2f * qd1 * qd3 * sin(q3);     // hip轴科里奥利力
    tau_inertia[2] = I3 * 0.1f * qd1 * qd2 * cos(q2);     // knee轴科里奥利力

    return tau_inertia;
}

void DisturbanceEstimator::update(const ControlFSMData<float>& data) {
    auto& estResult = data._stateEstimator->getResult(); // 获取状态估计结果
    _SetupCommand(data);
    float omegaworld = estResult.rpy[2];
    updateTauEst(data);
    // 更新PID控制器
    updatePIDControllers(data);

    // 这里可以将控制输出用于后续的控制逻辑
    // attitude_control_torque 包含姿态控制力矩
    // position_control_force 包含位置控制力

    // 可以将控制输出存储到类成员变量中，供其他模块使用
    T_b = attitude_control_torque;  // 期望质心扭矩
    F_b = position_control_force;   // 期望质心合外力
}

void DisturbanceEstimator::_SetupCommand(const ControlFSMData<float> & data){
  if(data._quadruped->_robotType == RobotType::MINI_CHEETAH){
    _body_height = 0.33;
  }else if(data._quadruped->_robotType == RobotType::CHEETAH_3){
    _body_height = 0.45;
  }else{
    assert(false);
  }

  float x_vel_cmd, y_vel_cmd;
  float filter(0.1);
  if(data.controlParameters->use_rc){
    const rc_control_settings* rc_cmd = data._desiredStateCommand->rcCommand;
    _yaw_turn_rate = -rc_cmd->omega_des[2];
    x_vel_cmd = rc_cmd->v_des[0];
    y_vel_cmd = rc_cmd->v_des[1] * 0.5;
    _body_height += rc_cmd->height_variation * 0.08;

  }else{
    _yaw_turn_rate = data._desiredStateCommand->rightAnalogStick[0]*1.5;
    x_vel_cmd = data._desiredStateCommand->leftAnalogStick[1]*1.5;//+0.07;
    y_vel_cmd = data._desiredStateCommand->leftAnalogStick[0];
  }
  _x_vel_des = _x_vel_des*(1-filter) + x_vel_cmd*filter;
  _y_vel_des = _y_vel_des*(1-filter) + y_vel_cmd*filter;

  _yaw_des = data._stateEstimator->getResult().rpy[2] + dtMPC * _yaw_turn_rate;
  _roll_des = 0.;
  _pitch_des = 0.;

}

// PID控制器初始化函数
void DisturbanceEstimator::initializePIDControllers() {
    // 姿态PID增益参数 (可根据实际需要调整)
    attitude_kp << 50.0f, 50.0f, 20.0f;     // Roll, Pitch, Yaw 比例增益
    attitude_ki << 5.0f, 5.0f, 2.0f;        // Roll, Pitch, Yaw 积分增益
    attitude_kd << 8.0f, 8.0f, 3.0f;        // Roll, Pitch, Yaw 微分增益

    // 角速度PID增益参数
    angular_velocity_kp << 10.0f, 10.0f, 5.0f;  // 角速度比例增益
    angular_velocity_kd << 2.0f, 2.0f, 1.0f;    // 角速度微分增益

    // 位置PID增益参数
    position_kp << 100.0f, 100.0f, 150.0f;  // X, Y, Z 位置比例增益
    position_ki << 10.0f, 10.0f, 15.0f;     // X, Y, Z 位置积分增益
    position_kd << 20.0f, 20.0f, 25.0f;     // X, Y, Z 位置微分增益

    // 速度PID增益参数
    velocity_kp << 50.0f, 50.0f, 60.0f;     // 速度比例增益
    velocity_kd << 5.0f, 5.0f, 6.0f;        // 速度微分增益

    // 初始化误差项
    attitude_error_integral.setZero();
    attitude_error_prev.setZero();
    angular_velocity_error_prev.setZero();
    position_error_integral.setZero();
    position_error_prev.setZero();
    velocity_error_prev.setZero();

    // 初始化控制输出
    attitude_control_torque.setZero();
    position_control_force.setZero();

    // 设置使能标志
    enable_attitude_pid = true;
    enable_position_pid = true;

    // 设置积分项限制
    attitude_integral_limit = 10.0f;  // 姿态积分项限制
    position_integral_limit = 50.0f;  // 位置积分项限制

    // 初始化调试变量
    enable_pid_debug = true;         // 默认关闭调试输出
    debug_counter = 0;
    debug_print_frequency = 500;      // 每500次更新输出一次调试信息
}

// 计算姿态误差（基于四元数）
Vec3<float> DisturbanceEstimator::computeAttitudeError(const Quat<float>& current_quat, const Quat<float>& desired_quat) {
    // 四元数顺序: [w, x, y, z] 即 q(0)=w, q(1)=x, q(2)=y, q(3)=z

    // 计算当前四元数的共轭: q_conj = [w, -x, -y, -z]
    Quat<float> current_quat_conj;
    current_quat_conj(0) = current_quat(0);   // w 保持不变
    current_quat_conj(1) = -current_quat(1);  // -x
    current_quat_conj(2) = -current_quat(2);  // -y
    current_quat_conj(3) = -current_quat(3);  // -z

    // 计算四元数误差: q_error = q_desired * q_current^(-1)
    // 四元数乘法: [w1, x1, y1, z1] * [w2, x2, y2, z2]
    Quat<float> q_error;
    q_error(0) = desired_quat(0)*current_quat_conj(0) - desired_quat(1)*current_quat_conj(1) -
                 desired_quat(2)*current_quat_conj(2) - desired_quat(3)*current_quat_conj(3);
    q_error(1) = desired_quat(0)*current_quat_conj(1) + desired_quat(1)*current_quat_conj(0) +
                 desired_quat(2)*current_quat_conj(3) - desired_quat(3)*current_quat_conj(2);
    q_error(2) = desired_quat(0)*current_quat_conj(2) - desired_quat(1)*current_quat_conj(3) +
                 desired_quat(2)*current_quat_conj(0) + desired_quat(3)*current_quat_conj(1);
    q_error(3) = desired_quat(0)*current_quat_conj(3) + desired_quat(1)*current_quat_conj(2) -
                 desired_quat(2)*current_quat_conj(1) + desired_quat(3)*current_quat_conj(0);

    // 确保四元数的标量部分为正（选择最短路径）
    if (q_error(0) < 0) {
        q_error = -q_error;  // 反转整个四元数
    }

    // 将四元数误差转换为轴角表示（旋转向量）
    Vec3<float> attitude_error;

    // 计算向量部分的范数 [x, y, z]
    float norm_q = sqrt(q_error(1)*q_error(1) + q_error(2)*q_error(2) + q_error(3)*q_error(3));

    if (norm_q < 1e-6f) {
        // 小角度近似: 旋转向量 ≈ 2 * [qx, qy, qz]
        attitude_error(0) = 2.0f * q_error(1);  // x
        attitude_error(1) = 2.0f * q_error(2);  // y
        attitude_error(2) = 2.0f * q_error(3);  // z
    } else {
        // 完整的轴角转换
        float angle = 2.0f * atan2(norm_q, fabs(q_error(0)));  // 计算旋转角度
        attitude_error(0) = (angle / norm_q) * q_error(1);     // x
        attitude_error(1) = (angle / norm_q) * q_error(2);     // y
        attitude_error(2) = (angle / norm_q) * q_error(3);     // z
    }

    return attitude_error;
}

// 计算角速度误差
Vec3<float> DisturbanceEstimator::computeAngularVelocityError(const Vec3<float>& current_omega, const Vec3<float>& desired_omega) {
    return desired_omega - current_omega;
}

// 计算位置误差
Vec3<float> DisturbanceEstimator::computePositionError(const Vec3<float>& current_pos, const Vec3<float>& desired_pos) {
    return desired_pos - current_pos;
}

// 计算速度误差
Vec3<float> DisturbanceEstimator::computeVelocityError(const Vec3<float>& current_vel, const Vec3<float>& desired_vel) {
    return desired_vel - current_vel;
}

// 姿态PID控制律
Vec3<float> DisturbanceEstimator::attitudePIDControl(const Vec3<float>& attitude_error, const Vec3<float>& angular_velocity_error) {
    if (!enable_attitude_pid) {
        return Vec3<float>::Zero();
    }

    // 比例项
    Vec3<float> proportional_term = attitude_kp.cwiseProduct(attitude_error);

    // 积分项
    attitude_error_integral += attitude_error * dtMPC;

    // 积分项限制（防止积分饱和）
    for (int i = 0; i < 3; i++) {
        if (attitude_error_integral[i] > attitude_integral_limit) {
            attitude_error_integral[i] = attitude_integral_limit;
        } else if (attitude_error_integral[i] < -attitude_integral_limit) {
            attitude_error_integral[i] = -attitude_integral_limit;
        }
    }

    Vec3<float> integral_term = attitude_ki.cwiseProduct(attitude_error_integral);

    // 微分项（基于姿态误差的微分）
    Vec3<float> attitude_error_derivative = (attitude_error - attitude_error_prev) / dtMPC;
    Vec3<float> derivative_term = attitude_kd.cwiseProduct(attitude_error_derivative);

    // 角速度反馈项（内环控制）
    Vec3<float> angular_velocity_feedback = angular_velocity_kp.cwiseProduct(angular_velocity_error);

    // 角速度微分项
    Vec3<float> angular_velocity_derivative = (angular_velocity_error - angular_velocity_error_prev) / dtMPC;
    Vec3<float> angular_velocity_damping = angular_velocity_kd.cwiseProduct(angular_velocity_derivative);

    // 总控制力矩
    Vec3<float> control_torque = proportional_term + integral_term + derivative_term +
                                angular_velocity_feedback + angular_velocity_damping;

    // 更新上一次误差
    attitude_error_prev = attitude_error;
    angular_velocity_error_prev = angular_velocity_error;

    return control_torque;
}

// 位置PID控制律
Vec3<float> DisturbanceEstimator::positionPIDControl(const Vec3<float>& position_error, const Vec3<float>& velocity_error) {
    if (!enable_position_pid) {
        return Vec3<float>::Zero();
    }

    // 比例项
    Vec3<float> proportional_term = position_kp.cwiseProduct(position_error);

    // 积分项
    position_error_integral += position_error * dtMPC;

    // 积分项限制（防止积分饱和）
    for (int i = 0; i < 3; i++) {
        if (position_error_integral[i] > position_integral_limit) {
            position_error_integral[i] = position_integral_limit;
        } else if (position_error_integral[i] < -position_integral_limit) {
            position_error_integral[i] = -position_integral_limit;
        }
    }

    Vec3<float> integral_term = position_ki.cwiseProduct(position_error_integral);

    // 微分项（基于位置误差的微分）
    Vec3<float> position_error_derivative = (position_error - position_error_prev) / dtMPC;
    Vec3<float> derivative_term = position_kd.cwiseProduct(position_error_derivative);

    // 速度反馈项（内环控制）
    Vec3<float> velocity_feedback = velocity_kp.cwiseProduct(velocity_error);

    // 速度微分项（阻尼项）
    Vec3<float> velocity_derivative = (velocity_error - velocity_error_prev) / dtMPC;
    Vec3<float> velocity_damping = velocity_kd.cwiseProduct(velocity_derivative);

    // 总控制力（或期望加速度）
    Vec3<float> control_force = proportional_term + integral_term + derivative_term +
                               velocity_feedback + velocity_damping + body_mass * acc_world_3d ;

    // 更新上一次误差
    position_error_prev = position_error;
    velocity_error_prev = velocity_error;

    return control_force;
}

// 设置PID增益参数
void DisturbanceEstimator::setPIDGains(const Vec3<float>& att_kp, const Vec3<float>& att_ki, const Vec3<float>& att_kd,
                                      const Vec3<float>& pos_kp, const Vec3<float>& pos_ki, const Vec3<float>& pos_kd) {
    attitude_kp = att_kp;
    attitude_ki = att_ki;
    attitude_kd = att_kd;
    position_kp = pos_kp;
    position_ki = pos_ki;
    position_kd = pos_kd;
}

// 更新PID控制器
void DisturbanceEstimator::updatePIDControllers(const ControlFSMData<float>& data) {
    // 获取当前状态
    auto& estResult = data._stateEstimator->getResult();

    // 当前状态
    Vec3<float> current_position = estResult.position;
    Vec3<float> current_velocity = estResult.vWorld;  // 使用世界坐标系速度
    Quat<float> current_orientation = estResult.orientation;
    Vec3<float> current_angular_velocity = estResult.omegaBody;  // 使用机体坐标系角速度

    // 期望状态（从命令中获取）
    Vec3<float> desired_position;
    Vec3<float> desired_velocity;
    Vec3<float> desired_rpy;
    Vec3<float> desired_angular_velocity;

    desired_position[0] = current_position[0] + dtMPC * _x_vel_des;
    desired_position[1] = current_position[1] + dtMPC * _y_vel_des;
    desired_position[2] = _body_height;

    desired_velocity[0] = _x_vel_des;
    desired_velocity[1] = _y_vel_des;
    desired_velocity[2] = 0.0f;

    desired_angular_velocity[0] = 0.0f;
    desired_angular_velocity[1] = 0.0f;
    desired_angular_velocity[2] = _yaw_turn_rate;

    desired_rpy[0] = 0.0f;
    desired_rpy[1] = 0.0f;
    desired_rpy[2] = _yaw_des;



    // 将期望RPY转换为四元数 (使用项目中的转换函数)
    Quat<float> desired_orientation = ori::rpyToQuat(desired_rpy);

    // 计算误差
    Vec3<float> position_error = computePositionError(current_position, desired_position);
    Vec3<float> velocity_error = computeVelocityError(current_velocity, desired_velocity);
    Vec3<float> attitude_error = computeAttitudeError(current_orientation, desired_orientation);
    Vec3<float> angular_velocity_error = computeAngularVelocityError(current_angular_velocity, desired_angular_velocity);

    // 执行PID控制
    if (enable_position_pid) {
        position_control_force = positionPIDControl(position_error, velocity_error);
    }

    if (enable_attitude_pid) {
        attitude_control_torque = attitudePIDControl(attitude_error, angular_velocity_error);
    }

    // 调试输出
    printPIDDebugInfo();
}



// 打印PID调试信息
void DisturbanceEstimator::printPIDDebugInfo() {
    if (!enable_pid_debug) return;

    debug_counter++;
    if (debug_counter >= debug_print_frequency) {
        debug_counter = 0;

        std::cout << "=== PID Controller Debug Info ===" << std::endl;

        // 姿态PID信息
        std::cout << "Attitude PID:" << std::endl;
        std::cout << "  Control Torque: [" << attitude_control_torque[0] << ", "
                  << attitude_control_torque[1] << ", " << attitude_control_torque[2] << "]" << std::endl;
        std::cout << "  Error Integral: [" << attitude_error_integral[0] << ", "
                  << attitude_error_integral[1] << ", " << attitude_error_integral[2] << "]" << std::endl;
        std::cout << "  Kp: [" << attitude_kp[0] << ", " << attitude_kp[1] << ", " << attitude_kp[2] << "]" << std::endl;
        std::cout << "  Ki: [" << attitude_ki[0] << ", " << attitude_ki[1] << ", " << attitude_ki[2] << "]" << std::endl;
        std::cout << "  Kd: [" << attitude_kd[0] << ", " << attitude_kd[1] << ", " << attitude_kd[2] << "]" << std::endl;

        // 位置PID信息
        std::cout << "Position PID:" << std::endl;
        std::cout << "  Control Force: [" << position_control_force[0] << ", "
                  << position_control_force[1] << ", " << position_control_force[2] << "]" << std::endl;
        std::cout << "  Error Integral: [" << position_error_integral[0] << ", "
                  << position_error_integral[1] << ", " << position_error_integral[2] << "]" << std::endl;
        std::cout << "  Kp: [" << position_kp[0] << ", " << position_kp[1] << ", " << position_kp[2] << "]" << std::endl;
        std::cout << "  Ki: [" << position_ki[0] << ", " << position_ki[1] << ", " << position_ki[2] << "]" << std::endl;
        std::cout << "  Kd: [" << position_kd[0] << ", " << position_kd[1] << ", " << position_kd[2] << "]" << std::endl;

        // 使能状态
        std::cout << "Enable Status:" << std::endl;
        std::cout << "  Attitude PID: " << (enable_attitude_pid ? "ON" : "OFF") << std::endl;
        std::cout << "  Position PID: " << (enable_position_pid ? "ON" : "OFF") << std::endl;
        std::cout << "=================================" << std::endl;
    }
}

// 启用/禁用PID调试
void DisturbanceEstimator::enablePIDDebug(bool enable) {
    enable_pid_debug = enable;
    debug_counter = 0;
}

// 重置PID积分项
void DisturbanceEstimator::resetPIDIntegrals() {
    attitude_error_integral.setZero();
    position_error_integral.setZero();
    std::cout << "PID integral terms have been reset." << std::endl;
}


// 创建反对称矩阵（叉积矩阵）
template<typename T>
Eigen::Matrix<T, 3, 3> skew(const Eigen::Matrix<T, 3, 1>& v) {
    Eigen::Matrix<T, 3, 3> m;
    m << 0, -v(2), v(1),
         v(2), 0, -v(0),
         -v(1), v(0), 0;
    return m;
}

//这个函数希望按照500hz的频率运行，去估计d_est
void DisturbanceEstimator::updateTauEst(const ControlFSMData<float>& data){
    auto& estResult = data._stateEstimator->getResult(); // 获取状态估计结果
    float yaw = estResult.rpy[2];
    float yc = cos(yaw);
    float ys = sin(yaw);
    Eigen::Matrix<float,3,3> R_yaw;
    R_yaw << yc,  -ys,   0,
            ys,  yc,   0,
            0,   0,   1;
    I_world = R_yaw * I_body * R_yaw.transpose();
    Eigen::Matrix<float,3,3> I_inv = I_world.inverse();

    Vec3<float> tau_contact_total; // 计算接触力计算的力矩

    for(int i = 0; i < 4; i++){
        Vec3<float> f = foot_force_tau_est[i];
        pFoot[i] = estResult.rBody.transpose() * (data._quadruped->getHipLocation(i) + 
                data._legController->datas[i].p);
        tau_contact_total += skew(pFoot[i]) * f;
    }
    // \dot{\hat{d}} = -M \left( I^{-1} \left( \sum r_{bci} \times F_i + \hat{d} + \beta + \sum r_i \times m_i g \right) \right).
    Vec3<float> dot_dhat = -K*(I_inv*(tau_contact_total + d_est + K*estResult.omegaWorld));
    d_est = d_est + dot_dhat * dt; 
}
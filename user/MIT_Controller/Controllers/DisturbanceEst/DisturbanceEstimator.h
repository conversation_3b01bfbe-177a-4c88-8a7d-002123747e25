#pragma once
#include "cppTypes.h"             // 若要用 Mat3<T>/Vec3<T> 这类别名
#include <eigen3/Eigen/Dense>     // 若直接用 Eigen::Matrix
using Eigen::Matrix;
#include <FSM_States/ControlFSMData.h>
class DisturbanceEstimator {
    public:
        EIGEN_MAKE_ALIGNED_OPERATOR_NEW
        DisturbanceEstimator(float _dt, int _iterations_between_mpc, MIT_UserParameters* parameters);
        ~DisturbanceEstimator(){};
        void initialize();
        void run(const ControlFSMData<float>& data);
        void update(const ControlFSMData<float>& data);
        void estimateFootForceFromTorque(const ControlFSMData<float>& data);
        Vec3<float> computeGravityCompensation(const Vec3<float>& q, int leg, const ControlFSMData<float>& data);
        Vec3<float> computeInertiaCompensation(const Vec3<float>& q, const Vec3<float>& qd, int leg, const ControlFSMData<float>& data);
        void fuseForceEstimates();
        void _SetupCommand(const ControlFSMData<float>& data);

        // PID控制器相关函数
        void initializePIDControllers();
        Vec3<float> computeAttitudeError(const Quat<float>& current_quat, const Quat<float>& desired_quat);
        Vec3<float> computeAngularVelocityError(const Vec3<float>& current_omega, const Vec3<float>& desired_omega);
        Vec3<float> computePositionError(const Vec3<float>& current_pos, const Vec3<float>& desired_pos);
        Vec3<float> computeVelocityError(const Vec3<float>& current_vel, const Vec3<float>& desired_vel);
        Vec3<float> attitudePIDControl(const Vec3<float>& attitude_error, const Vec3<float>& angular_velocity_error);
        Vec3<float> positionPIDControl(const Vec3<float>& position_error, const Vec3<float>& velocity_error);
        void updatePIDControllers(const ControlFSMData<float>& data);
        void setPIDGains(const Vec3<float>& att_kp, const Vec3<float>& att_ki, const Vec3<float>& att_kd,
                        const Vec3<float>& pos_kp, const Vec3<float>& pos_ki, const Vec3<float>& pos_kd);
        void printPIDDebugInfo();
        void enablePIDDebug(bool enable);
        void resetPIDIntegrals();
        
        void updateTauEst(const ControlFSMData<float>& data);

        float mp = 0.0f; //负载的估计质量
        float mp_torque_based = 0.0f; //基于扭矩估计的负载质量
        float mp_fused = 0.0f; //融合后的负载质量

        Vec4<float> contact_state; //接触状态，连续值
        Vec4<float> foot_force_est; //估计的足力 力传感器的
        Vec3<float> foot_force_tau_est[4]; //基于关节扭矩估计的足端接触力(三维)
        Vec4<float> foot_force_fused; //融合后的足端接触力估计
        float total_contact_force; //接触力之和 力传感器的
        Vec3 <float>  total_contact_force_tau_est; //基于关节扭矩估计的接触力之和
        Eigen::Matrix<float,3,3> I_body;
        Eigen::Matrix<float,3,3> I_world;
        float body_mass;
        Vec3<float> acc_world_3d;
        float torque_confidence = 1.0f;

        /* 扭矩和期望力的相关变量 */
        Vec3 <float> disturbance; //负载引起的扰动
        Vec3 <float> foot_force_des[4]; //期望的足力   
        Vec3<float> pFoot[4];     //足端位置相对于机身的坐标，世界坐标系下
    private:

        float dt;
        float dtMPC;
        int iterations_between_mpc;
        MIT_UserParameters* _parameters = nullptr;
        Vec3<float> gravity; //重力加速度

        // 成员变量建议：
        float mp_tau = 2.f;       // 低通时间常数(秒)，可调 1~5
        float mp_max = 10.f;      // 最大负载(kg)上限
        float last_contact_hys[4] = {0,0,0,0}; // 接触滞回状态(0/1)
        float F_min = 20.f;       // 单脚认为接触的最小 Fz 阈值(N)
        float total_mass_original; // 原始总质量，力传感器估计的

        // 扭矩估计相关参数
        bool enable_torque_estimation = true;  // 是否启用扭矩估计
        bool enable_gravity_compensation = false; // 是否启用重力补偿 - 暂时禁用进行调试
        bool enable_inertia_compensation = false; // 是否启用惯性补偿（可能不稳定）
        float torque_filter_alpha = 0.7f;     // 扭矩估计滤波系数
        float fusion_base_weight = 0.3f;      // 融合时扭矩估计的基础权重
        
        /* 扭矩和期望力的相关变量 */
        Vec3 <float> d_est; // 扭矩估计的负载扰动
        Eigen::Matrix<float,3,3> K; ///beta的权重矩阵
        Vec3 <float> input; // 辅助输入u；
        Vec3 <float> x_1; // theta
        Vec3 <float> x_2; // omega
        Vec3 <float> phi; // d_hat的更新率
        Vec3 <float> F_b; // 期望质心合外力
        Vec3 <float> T_b; // 期望质心扭矩

        // PID控制器相关变量
        // 姿态PID控制器
        Vec3<float> attitude_kp;        // 姿态比例增益
        Vec3<float> attitude_ki;        // 姿态积分增益
        Vec3<float> attitude_kd;        // 姿态微分增益
        Vec3<float> attitude_error_integral;  // 姿态误差积分项
        Vec3<float> attitude_error_prev;      // 上一次姿态误差
        Vec3<float> angular_velocity_kp;      // 角速度比例增益
        Vec3<float> angular_velocity_kd;      // 角速度微分增益
        Vec3<float> angular_velocity_error_prev; // 上一次角速度误差

        // 位置PID控制器
        Vec3<float> position_kp;        // 位置比例增益
        Vec3<float> position_ki;        // 位置积分增益
        Vec3<float> position_kd;        // 位置微分增益
        Vec3<float> position_error_integral;  // 位置误差积分项
        Vec3<float> position_error_prev;      // 上一次位置误差
        Vec3<float> velocity_kp;              // 速度比例增益
        Vec3<float> velocity_kd;              // 速度微分增益
        Vec3<float> velocity_error_prev;      // 上一次速度误差

        // PID控制器输出
        Vec3<float> attitude_control_torque;  // 姿态控制力矩输出
        Vec3<float> position_control_force;   // 位置控制力输出

        // PID控制器使能标志
        bool enable_attitude_pid;       // 是否启用姿态PID控制
        bool enable_position_pid;       // 是否启用位置PID控制

        // 积分项限制
        float attitude_integral_limit;  // 姿态积分项限制
        float position_integral_limit;  // 位置积分项限制

        // 调试相关变量
        bool enable_pid_debug;          // 是否启用PID调试输出
        int debug_counter;              // 调试输出计数器
        int debug_print_frequency;      // 调试输出频率（每N次更新输出一次）

        // 设置命令
        float _body_height;
        float _yaw_turn_rate;
        float _x_vel_des;
        float _y_vel_des;
        float _yaw_des;
        float _roll_des;
        float _pitch_des;

};
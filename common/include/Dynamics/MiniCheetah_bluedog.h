/*! @file MiniCheetah.h
 *  @brief Utility function to build a Mini Cheetah Quadruped object
 *
 * This file is based on MiniCheetahFullRotorModel_mex.m and builds a model
 * of the Mini Cheetah robot.  The inertia parameters of all bodies are
 * determined from CAD.
 *
 */

#ifndef PROJECT_MINICHEETAH_H
#define PROJECT_MINICHEETAH_H

#include "FloatingBaseModel.h"
#include "Quadruped.h"

/*!
 * Generate a Quadruped model of Mini Cheetah
 */
template <typename T>
Quadruped<T> buildMiniCheetah() {
  Quadruped<T> cheetah;
  cheetah._robotType = RobotType::MINI_CHEETAH;

  cheetah._bodyMass = 4.14993075;//5.5577;
  cheetah._bodyLength = 0.226;//0.185*2; 不明白为什么要乘以2
  cheetah._bodyWidth = 0.184;//0.045*2;
  cheetah._bodyHeight = 0.129;//0.115;  //todo
  cheetah._abadGearRatio = 6;
  cheetah._hipGearRatio = 6;
  cheetah._kneeGearRatio = 6;
  cheetah._abadLinkLength = 0.032;
  cheetah._hipLinkLength = 0.170;//
  cheetah._kneeLinkY_offset = 0;//
  cheetah._kneeLinkLength = 0.170;
  cheetah._maxLegLength = 0.170*2;//0.409;


  cheetah._motorTauMax =48.f;
  cheetah._batteryV = 48;
  cheetah._motorKT = .05;  // this is flux linkage * pole pairs
  cheetah._motorR = 0.173;
  cheetah._jointDamping = .01;
  cheetah._jointDryFriction = .2;
  //cheetah._jointDamping = .0;
  //cheetah._jointDryFriction = .0;


  // rotor inertia if the rotor is oriented so it spins around the z-axis
  Mat3<T> rotorRotationalInertiaZ;
  rotorRotationalInertiaZ << 10, 0, 0, 0, 10, 0, 0, 0, 100;
  rotorRotationalInertiaZ = 1e-6 * rotorRotationalInertiaZ;

  Mat3<T> RY = coordinateRotation<T>(CoordinateAxis::Y, M_PI / 2);
  Mat3<T> RX = coordinateRotation<T>(CoordinateAxis::X, M_PI / 2);
  Mat3<T> rotorRotationalInertiaX =
      RY * rotorRotationalInertiaZ * RY.transpose();
  Mat3<T> rotorRotationalInertiaY =
      RX * rotorRotationalInertiaZ * RX.transpose();

  // abad是FL_hip_link
  Mat3<T> abadRotationalInertia;
  abadRotationalInertia << 0.00013350, -0.00000421, 0.00000023,
                          -0.00000421, 0.00015397, -0.00000054,
                          0.00000023, -0.00000054, 0.00015612;
  Vec3<T> abadCOM(-0.00281235, 0.00604782, 0.00003916);  // LEFT
  SpatialInertia<T> abadInertia(0.34367492, abadCOM, abadRotationalInertia);//1.084

  // 大腿关节thigh
  Mat3<T> hipRotationalInertia;
  hipRotationalInertia << 0.00231532, 0.00003305, -0.00014453,
                            0.00003305, 0.00209890, 0.00027922,
                            -0.00014453, 0.00027922, 0.00071599;
  // Vec3<T> hipCOM(-0.00279154, -0.01613120, -0.02306211);
  Vec3<T> hipCOM(-0.0028337, -0.02375583, -0.02430315);
  SpatialInertia<T> hipInertia(0.72904635, hipCOM, hipRotationalInertia);

  Mat3<T> kneeRotationalInertia, kneeRotationalInertiaRotated;
  kneeRotationalInertiaRotated << 0.00053287, 0.00000000, 0.00001555,
                           0.00000000, 0.00053769, -0.00000052,
                           0.00001555, -0.00000052, 0.00001969;
  kneeRotationalInertia = RY * kneeRotationalInertiaRotated * RY.transpose();
  Vec3<T> kneeCOM(-0.00023140, 0.00000134, -0.09035571);
  SpatialInertia<T> kneeInertia(0.11634173, kneeCOM, kneeRotationalInertia);

  Vec3<T> rotorCOM(0, 0, 0);
  SpatialInertia<T> rotorInertiaX(0.135, rotorCOM, rotorRotationalInertiaX);
  SpatialInertia<T> rotorInertiaY(0.135, rotorCOM, rotorRotationalInertiaY);

  Mat3<T> bodyRotationalInertia;
  bodyRotationalInertia << 0.01243172, 0.00006602, 0.00378087,
                            0.00006602, 0.03910576, 0.00000778,
                            0.00378087, 0.00000778, 0.04291609;
  Vec3<T> bodyCOM(0.02687953, -0.01258310, 0.01688624);
  SpatialInertia<T> bodyInertia(cheetah._bodyMass, bodyCOM,
                                bodyRotationalInertia);

  cheetah._abadInertia = abadInertia;
  cheetah._hipInertia = hipInertia;
  cheetah._kneeInertia = kneeInertia;
  cheetah._abadRotorInertia = rotorInertiaX;
  cheetah._hipRotorInertia = rotorInertiaY;
  cheetah._kneeRotorInertia = rotorInertiaY;
  cheetah._bodyInertia = bodyInertia;

  // locations
  cheetah._abadRotorLocation = Vec3<T>(cheetah._bodyLength, cheetah._bodyWidth, 0) * 0.5;
  cheetah._abadLocation =Vec3<T>(cheetah._bodyLength, cheetah._bodyWidth, 0) * 0.5;

  cheetah._hipLocation = Vec3<T>(0, cheetah._abadLinkLength, 0);
  cheetah._hipRotorLocation = Vec3<T>(0,  cheetah._abadLinkLength, 0);

  cheetah._kneeLocation = Vec3<T>(0, 0, -cheetah._hipLinkLength);
  cheetah._kneeRotorLocation = Vec3<T>(0, 0, 0);

  return cheetah;

}

#endif  // PROJECT_MINICHEETAH_H







// template <typename T>
// Quadruped<T> buildMiniCheetah() {
//   Quadruped<T> cheetah;
//   cheetah._robotType = RobotType::MINI_CHEETAH;

//   cheetah._bodyMass = 6.336;
//   cheetah._bodyLength = 0.185*2;
//   cheetah._bodyWidth =0.045*2;//
//   cheetah._bodyHeight = 0.115;  //todo
//   cheetah._abadGearRatio = 6;
//   cheetah._hipGearRatio = 6;
//   cheetah._kneeGearRatio = 6;
//   cheetah._abadLinkLength = 0.10879;
//   cheetah._hipLinkLength = 0.198;//
//   cheetah._kneeLinkY_offset = 0;//
//   cheetah._kneeLinkLength = 0.205;
//   cheetah._maxLegLength = 0.198*2;//0.409;


//   cheetah._motorTauMax =48.f;
//   cheetah._batteryV = 48;
//   cheetah._motorKT = .05;  // this is flux linkage * pole pairs
//   cheetah._motorR = 0.173;
//   cheetah._jointDamping = .01;
//   cheetah._jointDryFriction = .2;
//   //cheetah._jointDamping = .0;
//   //cheetah._jointDryFriction = .0;


//   // rotor inertia if the rotor is oriented so it spins around the z-axis
//   Mat3<T> rotorRotationalInertiaZ;
//   rotorRotationalInertiaZ << 10, 0, 0, 0, 10, 0, 0, 0, 100;
//   rotorRotationalInertiaZ = 1e-6 * rotorRotationalInertiaZ;

//   Mat3<T> RY = coordinateRotation<T>(CoordinateAxis::Y, M_PI / 2);
//   Mat3<T> RX = coordinateRotation<T>(CoordinateAxis::X, M_PI / 2);
//   Mat3<T> rotorRotationalInertiaX =
//       RY * rotorRotationalInertiaZ * RY.transpose();
//   Mat3<T> rotorRotationalInertiaY =
//       RX * rotorRotationalInertiaZ * RX.transpose();

//   // spatial inertias
//   Mat3<T> abadRotationalInertia;
//   abadRotationalInertia << 300, 13, 0.28, 13, 371, -1.22, 0.28, -1.22, 342;
//   abadRotationalInertia = abadRotationalInertia * 1e-6;
//   Vec3<T> abadCOM(-0.00265287, 0.00246795, -0.00009144);  // LEFT
//   SpatialInertia<T> abadInertia(0.554, abadCOM, abadRotationalInertia);//1.084

//   Mat3<T> hipRotationalInertia;
//   hipRotationalInertia << 3119, 61, -183, 61, 2859, 462, -183, 462, 972;
//   hipRotationalInertia = hipRotationalInertia * 1e-6;
//   Vec3<T> hipCOM(-0.00261152, -0.02565065, -0.02170329);
//   SpatialInertia<T> hipInertia(1.0189, hipCOM, hipRotationalInertia);

//   Mat3<T> kneeRotationalInertia, kneeRotationalInertiaRotated;
//   kneeRotationalInertiaRotated << 979, -0.05, 21, -0.05, 988, -0.92, 21, -0.92, 34;
//   kneeRotationalInertiaRotated = kneeRotationalInertiaRotated * 1e-6;
//   kneeRotationalInertia = RY * kneeRotationalInertiaRotated * RY.transpose();
//   Vec3<T> kneeCOM(-0.00061659, 0.00003269, -0.11239405);
//   SpatialInertia<T> kneeInertia(0.2, kneeCOM, kneeRotationalInertia);

//   Vec3<T> rotorCOM(0, 0, 0);
//   SpatialInertia<T> rotorInertiaX(0.135, rotorCOM, rotorRotationalInertiaX);
//   SpatialInertia<T> rotorInertiaY(0.135, rotorCOM, rotorRotationalInertiaY);

//   Mat3<T> bodyRotationalInertia;
//   bodyRotationalInertia << 22465, -181, 1449, -181, 85458, 124, 1449, 124, 87061;
//   bodyRotationalInertia = bodyRotationalInertia * 1e-6;
//   Vec3<T> bodyCOM(0, 0, 0);
//   SpatialInertia<T> bodyInertia(cheetah._bodyMass, bodyCOM,
//                                 bodyRotationalInertia);

//   cheetah._abadInertia = abadInertia;
//   cheetah._hipInertia = hipInertia;
//   cheetah._kneeInertia = kneeInertia;
//   cheetah._abadRotorInertia = rotorInertiaX;
//   cheetah._hipRotorInertia = rotorInertiaY;
//   cheetah._kneeRotorInertia = rotorInertiaY;
//   cheetah._bodyInertia = bodyInertia;

//   // locations
//   cheetah._abadRotorLocation = Vec3<T>(cheetah._bodyLength, cheetah._bodyWidth, 0) * 0.5;
//   cheetah._abadLocation =Vec3<T>(cheetah._bodyLength, cheetah._bodyWidth, 0) * 0.5;

//   cheetah._hipLocation = Vec3<T>(0, cheetah._abadLinkLength, 0);
//   cheetah._hipRotorLocation = Vec3<T>(0,  cheetah._abadLinkLength, 0);

//   cheetah._kneeLocation = Vec3<T>(0, 0, -cheetah._hipLinkLength);
//   cheetah._kneeRotorLocation = Vec3<T>(0, 0, 0);

//   return cheetah;

// }

// #endif  // PROJECT_MINICHEETAH_H

